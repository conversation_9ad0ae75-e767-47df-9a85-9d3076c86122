package com.ybmmarket20.activity

import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.xyyreport.page.searchOrder.SearchOrderStartPageReport
import com.ybmmarket20.xyyreport.spm.SpmBean

abstract class SearchOrderAnalysisActivity: BaseActivity() {

    private var mSearchOrderStartPageAnalysis: SearchOrderStartPageAnalysis? = null

    override fun initData() {
        mSearchOrderStartPageAnalysis = SearchOrderStartPageAnalysis()
        mSearchOrderStartPageAnalysis!!.pvTrack()
    }

    inner class SearchOrderStartPageAnalysis {

        private var mStartPgeSpmCnt: SpmBean? = null

        fun pvTrack() {
            mStartPgeSpmCnt = SearchOrderStartPageReport.pvTrack(this@SearchOrderAnalysisActivity)
        }
    }
}