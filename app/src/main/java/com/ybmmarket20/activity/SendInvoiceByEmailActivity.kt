package com.ybmmarket20.activity

import android.text.TextUtils
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.Observer
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.viewmodel.SendInvoiceByEmailViewModel
import kotlinx.android.synthetic.main.activity_send_invoice_by_email.*

/**
 * 通过邮箱发送发票
 */
@Router("sendinvoicebyemail")
class SendInvoiceByEmailActivity: BaseActivity() {

    val mViewModel: SendInvoiceByEmailViewModel by viewModels()
    var orderId: String = ""

    override fun getContentViewId(): Int = R.layout.activity_send_invoice_by_email

    override fun initData() {
        setTitle("邮箱地址")
        orderId = intent.getStringExtra("orderId")?: ""
        tv_post.setOnClickListener {
            hideSoftInput(et_email)
            val emailAddress = et_email.text.toString().trim { it <= ' ' }
            if (TextUtils.isEmpty(emailAddress)) {
                ToastUtils.showShort("请输入邮箱地址")
                return@setOnClickListener
            }
            if (!UiUtils.isEmail(emailAddress)) {
                tv_mail_format_tip.visibility = View.VISIBLE
                return@setOnClickListener
            }
            mViewModel.sendInvoiceEmailForOrder(orderId, et_email.text.toString())
        }
        mViewModel.sendEmailLiveData.observe(this, Observer {
            if (it.isSuccess) {
                ToastUtils.showShort("发送成功")
                finish()
            }
        })
        et_email.addTextChangedListener(afterTextChanged = {
            //监听输入框
            if (!TextUtils.isEmpty(it)) {
                tv_post.isClickable = true
                tv_post.setBackgroundColor(ContextCompat.getColor(this, R.color.color_00B377))
            } else {
                tv_post.isClickable = false
                tv_post.setBackgroundColor(ContextCompat.getColor(this, R.color.color_A9AEB7))
            }
        })
    }
}