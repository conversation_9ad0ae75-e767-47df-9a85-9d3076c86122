package com.ybmmarket20.activity

import android.content.Context
import android.os.Bundle
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.xyyreport.page.pdfCheck.PDFCheckReport

abstract class AptitudeXyyPdfAnalysisActivity: BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        pvTrack()
    }

    fun pvTrack() {
        val ft = intent.getStringExtra("fileType")
        val fId = intent.getStringExtra("fileId")
        PDFCheckReport.pvTrack(this, ft, fId)
    }

    fun clickShareBtn(orderNo: String?, isOrder: String?) {
        PDFCheckReport.pdfCheckShareClick(this, orderNo, isOrder)
    }

    fun clickDownloadBtn(orderNo: String?, isOrder: String?) {
        PDFCheckReport.pdfCheckDownloadClick(this, orderNo, isOrder)
    }
}