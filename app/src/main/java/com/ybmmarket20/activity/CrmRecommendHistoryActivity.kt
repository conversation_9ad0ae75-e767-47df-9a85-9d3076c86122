package com.ybmmarket20.activity

import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.reflect.TypeToken
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.view.CommonRecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.adapter.CrmRecommendHistoryAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RecommendHistoryBean
import com.ybmmarket20.bean.RefreshCrmRecommendBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.activity_crm_recommend.*
import java.lang.reflect.Type

/**
 * <AUTHOR> Brin
 * @date : 2020/6/28 - 14:35
 * @Description :
 * @version
 */
@Router("crmrecommendhistoryactivity")
class CrmRecommendHistoryActivity : RefreshWrapperActivity<RecommendHistoryBean>() {

    private var adapter: CrmRecommendHistoryAdapter? = null

    override fun initData() {
        super.initData()
        setTitle("推荐列表")
        loadData()
    }

    override fun getRequestParams(): RequestParams {
        val params = RequestParams()
        return params
    }

    override fun getAdapter(rows: List<RecommendHistoryBean>): YBMBaseAdapter<RecommendHistoryBean> {
        if (adapter == null) {
            adapter = CrmRecommendHistoryAdapter(R.layout.item_crm_recommend_history, rows)
        }
        return adapter as CrmRecommendHistoryAdapter
    }

    override fun getType(): Type = object : TypeToken<BaseBean<RefreshCrmRecommendBean<RecommendHistoryBean>>>() {}.type

    override fun getUrl(): String = AppNetConfig.CRM_RECOMMEND_RECORD_LIST

    override fun getCommonRecyclerView(): CommonRecyclerView = crl_commend

    override fun getContentViewId(): Int = R.layout.activity_crm_recommend_history

    override fun getStartPage() = 0

    override fun getPageName(): String? = XyyIoUtil.PAGE_RECOMMENDED_HISTORY

}