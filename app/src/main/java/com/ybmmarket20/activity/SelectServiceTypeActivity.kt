package com.ybmmarket20.activity

import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_INVOICE
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_LICENSE
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.bean.ServiceType
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.OrderDetailViewModel
import com.ybmmarket20.viewmodel.SelectServiceTypeViewModel
import kotlinx.android.synthetic.main.activity_select_service_type.*
import okio.ByteString
import okio.ByteString.Companion.decodeBase64
import java.nio.charset.Charset

/**
 * 选择服务类型
 */

//退货退款
const val SERVICE_TYPE_REFUND_GOODS = 1

//仅退款
const val SERVICE_TYPE_REFUND = 2

@Router("selectservicetype", "selectservicetype/:status/:orderNo/:orderId", "selectservicetype/:status/:orderNo/:orderId/:origName")
class SelectServiceTypeActivity : BaseActivity() {

    val status: String? by lazy { intent.getStringExtra("status") }
    val orderNo: String? by lazy { intent.getStringExtra("orderNo") }
    val orderId: String? by lazy { intent.getStringExtra("orderId") }
    val origName: String? by lazy { intent.getStringExtra("origName") }
    private val mOrderDetailViewModel: OrderDetailViewModel by viewModels()

    override fun getContentViewId(): Int = R.layout.activity_select_service_type

    override fun initData() {
        setTitle("服务类型")
        getData()
    }

    fun getData() {
        val viewModel = ViewModelProvider(this).get(SelectServiceTypeViewModel::class.java)
        showProgress()
        viewModel.selectServiceTypeLiveData.observe(this, Observer {
            val adapter = ServiceTypeAdapter(it)
            tv_service_type.layoutManager =
                WrapLinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
            tv_service_type.adapter = adapter
            dismissProgress()
        })
        mOrderDetailViewModel.afterSaleTipsLiveData.observe(this) {
            RoutersUtils.open(it)
        }
        viewModel.getServiceType(orderNo)
    }

    inner class ServiceTypeAdapter(serviceTypeList: MutableList<ServiceType>) :
        YBMBaseAdapter<ServiceType>(R.layout.item_service_type, serviceTypeList) {

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ServiceType?) {
            whenAllNotNull(baseViewHolder, t) { holder, serviceType ->
                val serviceTypeStatus = when(serviceType.afterType) {
                    //发票售后
                    1 -> ServiceTypeStatus.InvoiceServiceTypeStatus(serviceType, holder, mOrderDetailViewModel, origName)
                    //资质售后
                    2 -> ServiceTypeStatus.AptitudeServiceTypeStatus(serviceType, holder, mOrderDetailViewModel, origName)
                    //退货退款
                    else -> ServiceTypeStatus.RefundServiceTypeStatus(serviceType, holder, status)
                }
                serviceTypeStatus.onBindData()
                holder.itemView.setOnClickListener {
                    serviceTypeStatus.onClick()
                }
            }
        }
    }

    sealed class ServiceTypeStatus(
        private val serviceType: ServiceType,
        private val holder: YBMBaseHolder
    ) {
        fun onBindData() {
            holder.getView<TextView>(R.id.tv_title).text = serviceType.title
            holder.getView<TextView>(R.id.tv_sub_title).text = serviceType.desc
            if (getIconResource() != -1) {
                holder.getView<ImageView>(R.id.iv_icon).setImageResource(getIconResource())
            }
        }

        open fun getIconResource(): Int = -1
        open fun onClick() {}

        /**
         * 发票售后
         */
        class InvoiceServiceTypeStatus(
            private val serviceType: ServiceType,
            holder: YBMBaseHolder,
            private val orderDetailViewModel: OrderDetailViewModel,
            private val origName: String?
        ) : ServiceTypeStatus(serviceType, holder) {
            override fun onClick() {
                super.onClick()
                val orderDetailBean = CheckOrderDetailBean()
                orderDetailBean.orderNo = serviceType.orderNo
                orderDetailBean.orgId = serviceType.orgId
                orderDetailBean.origName = origName
                if (serviceType.afterSalesStatus == 0) {
                    //申请
                    orderDetailViewModel.getAfterSalesInfo(orderDetailBean, TIPS_TYPE_INVOICE)
                } else {
                    RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo=${serviceType.afterSalesNo}")
                }
            }

            override fun getIconResource(): Int = R.drawable.icon_service_type_invoice_after_sale
        }

        /**
         * 资质售后
         */
        class AptitudeServiceTypeStatus(
            private val serviceType: ServiceType,
            holder: YBMBaseHolder,
            private val orderDetailViewModel: OrderDetailViewModel,
            private val origName: String?
        ) : ServiceTypeStatus(serviceType, holder) {
            override fun onClick() {
                super.onClick()
                val orderDetailBean = CheckOrderDetailBean()
                orderDetailBean.orderNo = serviceType.orderNo
                orderDetailBean.orgId = serviceType.orgId
                orderDetailBean.origName = origName
                if (serviceType.afterSalesStatus == 0) {
                    //申请
                    orderDetailViewModel.getAfterSalesInfo(orderDetailBean, TIPS_TYPE_LICENSE)
                } else {
                    RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo=${serviceType.afterSalesNo}")
                }
            }

            override fun getIconResource(): Int = R.drawable.icon_service_type_aptitude_after_sale
        }

        /**
         * 退货退款
         */
        class RefundServiceTypeStatus(
            private val serviceType: ServiceType,
            holder: YBMBaseHolder,
            private val status: String?
        ) : ServiceTypeStatus(serviceType, holder) {
            override fun onClick() {
                super.onClick()
                var preRouterStr = "ybmpage://choiceproduct/${status}/${serviceType.orderNo}/${serviceType.orderId}/"
                if (serviceType.id == 1) {
                    preRouterStr += "1"
                }
                RoutersUtils.open("$preRouterStr/${serviceType.id}")
            }

            override fun getIconResource(): Int = R.drawable.icon_service_type_refund
        }

    }

}