package com.ybmmarket20.activity

import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.adapter.OrderDetailAdapterNew
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.view.DividerLine
import com.ybmmarket20.viewmodel.OrderDetailProductListViewModel
import kotlinx.android.synthetic.main.activity_order_detail_product.rv_list

/**
 * 订单详情商品明细
 */
@Router("orderDeailProductListActivity/:order_no")
class OrderDetailProductListActivity : BaseActivity() {
    private var orderNo = ""
    private var adapter: OrderDetailAdapterNew? = null
    private val viewModel: OrderDetailProductListViewModel by viewModels()
    override fun getContentViewId() = R.layout.activity_order_detail_product

    override fun initData() {
        orderNo = intent?.getStringExtra(IntentCanst.ORDER_NO) ?: ""
        setTitle("商品明细")
        val divider = DividerLine(DividerLine.VERTICAL)
        divider.setSize(1)
        divider.setColor(-0xa0a0b)
        rv_list.addItemDecoration(divider)
        rv_list.layoutManager = LinearLayoutManager(this)
        showProgress()
        viewModel.getOrderProductList(orderNo)
        viewModel.onOrderProductLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                val rowsBeans = ArrayList<RefundProductListBean>()
                if (it.data.detailList != null && it.data.detailList?.size!! > 0) {
                    rowsBeans.addAll(it.data.detailList!!)
                }
                if (it.data.packageList != null && it.data.packageList?.size!! > 0) {
                    it.data?.packageList?.forEach { packageBean->
                        if (packageBean?.orderDetailList!=null&&packageBean.orderDetailList?.size!!>0){
                            rowsBeans.addAll(packageBean.orderDetailList)
                        }
                    }
                }
                adapter = OrderDetailAdapterNew(rowsBeans.toMutableList())
                rv_list.adapter = adapter
            }
        }
    }
}