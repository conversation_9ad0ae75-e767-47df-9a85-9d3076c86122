package com.ybmmarket20.activity

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import com.flyco.tablayout.listener.OnTabSelectListener
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.fragments.ShopAfterSaleDistributionPopFragment
import com.ybmmarket20.fragments.ShopAfterSaleDistributionSelfFragment
import com.ybmmarket20.fragments.ShopOpenAccountFragment
import com.ybmmarket20.fragments.ShopQualificationPopFragment
import com.ybmmarket20.fragments.ShopQualificationSelfFragment
import com.ybmmarket20.utils.analysis.XyyIoUtil
import kotlinx.android.synthetic.main.activity_qualification_and_after_sale.tabLayout
import kotlinx.android.synthetic.main.activity_qualification_and_after_sale.vp

/**
 * 资质/配送
 */
@Router("qualificationandaftersale")
class QualificationAndAfterSaleActivity: BaseActivity() {

    override fun getContentViewId(): Int = R.layout.activity_qualification_and_after_sale

    override fun initData() {
        val shopCode = intent.getStringExtra("shopCode")
        XyyIoUtil.track("shopHome_licenseAfterSale", hashMapOf("page_name" to "资质/配送"))
        //自营: 1, pop: 0
        val isSelf = try {
            intent.getStringExtra("isSelf")?.toInt()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val titles = arrayListOf("资质信息")
        if (isSelf == 0) {
            titles.add("开户流程")
        }
        titles.add("配送售后")
        tabLayout.setTitles(titles)
        val fragments = if (isSelf == 1) setSelfFragment(shopCode?: "")
        else setPopFragment(shopCode?: "")
        val adapter = QualificationAndAfterSaleAdapter(supportFragmentManager, fragments)
        vp.adapter = adapter
        tabLayout.setViewPager(vp)
        tabLayout.setOnTabSelectListener(object : OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                XyyIoUtil.track("shopHome_licenseAfterSale_Menu_Click", hashMapOf(
                    "shop_code" to shopCode,
                    "text" to titles[position]
                ))
            }

            override fun onTabReselect(position: Int) {
            }

        })
    }


    /**
     * 设置自营Fragment集合
     */
    private fun setSelfFragment(shopCode: String): List<Fragment> {
        return listOf<Fragment>(ShopQualificationSelfFragment(), ShopAfterSaleDistributionSelfFragment()).map {
            it.arguments = Bundle().apply {
                putString("params", shopCode)
                putString("shopCode", shopCode)
            }
            it
        }
    }

    /**
     * 设置pop Fragment集合
     */
    private fun setPopFragment(shopCode: String): List<Fragment> {
        return listOf<Fragment>(ShopQualificationPopFragment(), ShopOpenAccountFragment(), ShopAfterSaleDistributionPopFragment()).map {
            it.arguments = Bundle().apply {
                putString("params", shopCode)
                putString("orgId", shopCode)
            }
            it
        }
    }

    @SuppressLint("WrongConstant")
    inner class QualificationAndAfterSaleAdapter(fm: FragmentManager, val fragments: List<Fragment>): FragmentStatePagerAdapter(fm, BEHAVIOR_SET_USER_VISIBLE_HINT) {
        override fun getCount(): Int = fragments.size

        override fun getItem(position: Int): Fragment = fragments[position]

    }
}