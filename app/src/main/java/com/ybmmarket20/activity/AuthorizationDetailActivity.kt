package com.ybmmarket20.activity

import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.adapter.AuthorizationDetailAdapter
import com.ybmmarket20.bean.AuthorizationAreaRowBean
import com.ybmmarket20.bean.AuthorizationDetailBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.*
import com.ybmmarket20.common.eventbus.Event
import com.ybmmarket20.common.eventbus.EventBusUtil
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.IntentCanst.RX_BUS_AGENT_ORDER_AUTHORIZATION_STATUS
import com.ybmmarket20.fragments.AGENT_ORDER_AUTHORIZATION_FLAG_READ
import com.ybmmarket20.fragments.AGENT_ORDER_AUTHORIZATION_FLAG_UNREAD
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import kotlinx.android.synthetic.main.activity_authorization_detail.*

/**
 * 代下单授权详情
 */
@Router("authorizeOrderDetail")
class AuthorizationDetailActivity : BaseActivity() {

    var authorizeId: String = ""
    var adapter: AuthorizationDetailAdapter? = null
    lateinit var dialog: AlertDialogEx
    var readFlag: Int = 0
    var authorizationBean: AuthorizationAreaRowBean? = null

    override fun getContentViewId(): Int = R.layout.activity_authorization_detail

    override fun initData() {
        authorizationBean = intent.getParcelableExtra("authorizeBean")
        if (authorizationBean == null) {
            authorizeId = intent.getStringExtra("authorize_id") ?: ""
            authorizationBean = AuthorizationAreaRowBean()
            authorizationBean?.id = authorizeId
            authorizationBean?.readFlag = AGENT_ORDER_AUTHORIZATION_FLAG_READ
        } else {
            authorizeId = authorizationBean?.id ?: ""
            readFlag = authorizationBean?.readFlag ?: 0
        }
        getData(authorizeId)
        tv_agree_authorization.setOnClickListener {
            dialog = AlertDialogEx(this).apply {
                setTitle(resources.getString(R.string.title_desc))
                setMessage("是否确认同意授权处理代下单")
                setCancelButton(resources.getString(R.string.cancel)) { _, _ -> dismiss() }
                setConfirmButton(resources.getString(R.string.confirm)) { _, _ -> agreeAuthorization() }
                show()
            }
        }
        tv_applicant_phone_text.setOnClickListener {
            tv_applicant_phone_text?.text.let {
                RoutersUtils.telPhone(true, it.toString())
            }
        }
    }

    /**
     * 获取数据
     */
    private fun getData(authorizeId: String) {
        showProgress()
        val params = RequestParams().apply {
            put("authorizeId", authorizeId)
            put("merchantId", SpUtil.getMerchantid())
        }
        HttpManager.getInstance().post(AppNetConfig.AGENT_ORDER_AUTHORIZATION_Detail, params, object : BaseResponse<AuthorizationDetailBean>() {
            override fun onSuccess(content: String?, obj: BaseBean<AuthorizationDetailBean>?, t: AuthorizationDetailBean?) {
                super.onSuccess(content, obj, t)
                dismissProgress()
                if (obj == null || !obj.isSuccess || t == null) return
                tv_authorization_title.text = t.title
                tv_applicant_text.text = t.applicant
                tv_submit_time_text.text = t.createTime
                tv_applicant_phone_text.text = t.mobile
                tv_authorization_count.text = "${getString(R.string.authorization_goods_category)}(${t.productDetail?.size ?: "0"})"
                adapter = AuthorizationDetailAdapter(R.layout.item_authorization_detail, t.productDetail)
                rv_authorization_detail.layoutManager = LinearLayoutManager(this@AuthorizationDetailActivity, LinearLayoutManager.VERTICAL, false)
                rv_authorization_detail.adapter = adapter
                iv_authorization_status.setImageResource(if (t.status == 0) R.drawable.icon_unauthorization else R.drawable.icon_authorizationed)
                cl_authorization_bottom.visibility = if (t.status == 0) View.VISIBLE else View.GONE
                //发送已读消息
                if (readFlag == AGENT_ORDER_AUTHORIZATION_FLAG_UNREAD) {
                    EventBusUtil.sendEvent(Event<String>(IntentCanst.RX_BUS_AGENT_ORDER_AUTHORIZATION_READ_FLAG, authorizeId))
                }
                val applyContent = "尊敬的客户：\n您好!\n         我是${t.applicant}，感谢您一直以来对本人的信任和支持！\n         根据公司总部战略规划，我公司已和武汉小药药医药科技有限公司达成长期合作，以下品种（见底部品种列表）已转移至药帮忙平台进行销售，不再通过其他渠道进行销售，团队成员将专注为大家提供专业的线下产品推广服务，原有的其他合作方式不变，此次销售渠道调整旨在向广大客户提供更多专业、优质、高效的营销推广服务，欢迎您在药帮忙平台指定的控销商城专区进行产品选购。\n         为了能够给您提供更加全面的线上和线下产品营销推广服务，需要得到您配合与支持，请您对以下事项进行授权：\n         1、授权本人在和您沟通后代替您制作采购订单，并将订单推送至您的药帮忙“我的”APP代下单专区；\n         2、订单确认：您在收到订单信息后需要核对确认订单信息，并在48小时内（大促活动为12小时）完成订单确认操作，确认后您需要在48小时内（大促活动为12小时）完成支付，在此时间限内我们将为您锁定订单产品库存确保您能购买到订单产品；\n         3、问题处理：如果此时间限内您有疑问也可及时联系我咨询后再确定订单如何操作，如以上订单信息若有误时请您及时联系我重新制作订单，您可以选择确认购买或驳回订单，订单品种仅限于品种列表所列品种；\n         4、品种维护：若品种列表涉及的品种有删减或增补，或有价格信息的调整，本人也将第一时间告知于您，同时也请您配合做好产品零售价格的维护工作。\n         再次感谢您一直以来对本人工作的支持！\n顺祝\n         商祺！"
                tv_submit_content_text_local.text = applyContent
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
            }
        })
    }

    /**
     * 同意授权
     */
    private fun agreeAuthorization() {
        val params = RequestParams().apply {
            put("authorizeId", authorizeId)
            put("merchantId", SpUtil.getMerchantid())
        }
        showProgress()
        HttpManager.getInstance().post(AppNetConfig.AGENT_ORDER_AUTHORIZATION_AGREE, params, object : BaseResponse<Any>() {
            override fun onSuccess(content: String?, obj: BaseBean<Any>?, t: Any?) {
                super.onSuccess(content, obj, t)
                if (obj != null && obj.isSuccess()) {
                    dismissProgress()
                    iv_authorization_status.setImageResource(R.drawable.icon_authorizationed)
                    cl_authorization_bottom.visibility = View.GONE
                    dialog.dismiss()
                    //发送授权消息
                    if (authorizationBean != null) {
                        authorizationBean!!.status = AUTHORIZATION_AREA_AUTHORIZATIONED
                        authorizationBean!!.readFlag = AGENT_ORDER_AUTHORIZATION_FLAG_READ
                        EventBusUtil.sendEvent(Event<AuthorizationAreaRowBean>(RX_BUS_AGENT_ORDER_AUTHORIZATION_STATUS, authorizationBean))
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
            }
        })
    }
}